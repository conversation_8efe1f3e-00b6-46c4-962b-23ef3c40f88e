<template>
  <div class="w-full">
    <BaseEchart v-if="options" :options="options" height="500px" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import BaseEchart from '@/BaseComponent/BaseEchart/src/echart.vue'
import { ElMessage } from 'element-plus'
import { apiDataDataEquipmentUtilization } from '@/apis/data'
import { ToolUtils } from '@/utils/tool'
import type { TDataEquipmentUtilizationStats } from '@/apis/model'

interface Props {
  equipmentType: number
  dateRange: {
    viewType: string
    startDate: string
    endDate: string
  }
}

const props = defineProps<Props>()

// 图表数据
const chartData = ref<TDataEquipmentUtilizationStats[]>([])
const lastViewType = ref('daily')

// 获取图表数据
const fetchChartData = async () => {
  try {
    if (!props.dateRange?.viewType || !props.dateRange?.startDate || !props.dateRange?.endDate) {
      return
    }

    const params = {
      equipmentType: props.equipmentType,
      startTime: props.dateRange.startDate,
      endTime: props.dateRange.endDate,
      timeType: props.dateRange.viewType
    }

    const res = await apiDataDataEquipmentUtilization(params)
    chartData.value = res || []
    lastViewType.value = props.dateRange.viewType
  } catch (error) {
    console.error('获取设备作业率数据失败:', error)
    ElMessage.error('获取数据失败')
  }
}

// 监听参数变化
watch( [() => props.equipmentType, () => props.dateRange], () => {
  fetchChartData()
}, {
  immediate: true,
  deep: true
})

// 格式化日期显示
const formatDisplayDate = (item: TDataEquipmentUtilizationStats) => {
  if (lastViewType.value === 'daily') {
    return item.startDate || ''
  } else if (lastViewType.value === 'monthly') {
    return `${item.year}年${item.month}月`
  } else if (lastViewType.value === 'weekly') {
    return `${item.year}年第${item.weekNumber}周`
  }
  return item.startDate || ''
}

// 图表配置
const options = computed(() => {
  if (!chartData.value || !chartData.value.length) {
    return {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      xAxis: {
        type: 'category',
        data: []
      },
      yAxis: [
        {
          type: 'value',
          name: '实际工作时间(小时)'
        },
        {
          type: 'value',
          name: '作业率(%)',
          position: 'right'
        }
      ],
      series: []
    }
  }

  const xAxisData = chartData.value.map((item: TDataEquipmentUtilizationStats) => formatDisplayDate(item))
  const actualWorkTimeData = chartData.value.map((item: TDataEquipmentUtilizationStats) => item.actualWorkTime || 0)
  const utilizationRateData = chartData.value.map((item: TDataEquipmentUtilizationStats) => {
    const rate = item.utilizationRate || 0
    return Math.round(rate * 100) / 100
  })

  const getMax = (arr: number[]) => {
    let max = Math.round(Math.max(...arr))
    if (max === 0) return 100
    return (Number(max.toString()[0]) + 1) * Math.pow(10, max.toString().length - 1)
  }
  
  const actualWorkTimeMax = getMax(actualWorkTimeData)
  const utilizationRateMax = getMax(utilizationRateData)

  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: function(params: any) {
        const axisValue = params[0].axisValue;
        let result = axisValue + '<br/>';
        
        // 添加作业率和实际工作时间
        params.forEach((item: any) => {
          const value = item.value;
          const seriesName = item.seriesName;
          const color = item.color;
          const unit = seriesName.includes('作业率') ? '%' : '小时';

          result += `<div style="display:flex;align-items:center;margin:5px 0;">
            <span style="display:inline-block;width:10px;height:10px;background:${color};border-radius:50%;margin-right:5px;"></span>
            ${seriesName}: <strong>${value}${unit}</strong>
          </div>`;
        });
        
        return result;
      }
    },
    legend: {
      data: ['实际工作时间', '作业率'],
      bottom: 0,
    },
    grid: {
      left: '3%',
      right: '8%',
      bottom: '10%',
      containLabel: true,
    },
    xAxis: [
      {
        type: 'category',
        boundaryGap: ['5%', '5%'],
        data: xAxisData,
        axisPointer: {
          type: 'shadow',
        },
        axisLabel: {
          rotate: 0,
          margin: 15,
          overflow: 'truncate',
          formatter: (value: string) => {
            if (lastViewType.value === 'monthly') {
              return value.replace('年', '年\n')
            }
            return value
          },
        },
      },
    ],
    yAxis: [
      {
        type: 'value',
        name: '实际工作时间(小时)',
        min: 0,
        max: actualWorkTimeMax,
        interval: actualWorkTimeMax / 5,
        axisLabel: {
          formatter: '{value}',
        },
        axisLine: {
          lineStyle: {
            color: '#5B9BD5',
          },
        },
        nameTextStyle: {
          color: '#5B9BD5',
        },
      },
      {
        type: 'value',
        name: '作业率(%)',
        min: 0,
        max: utilizationRateMax,
        interval: utilizationRateMax / 5,
        position: 'right',
        axisLabel: {
          formatter: '{value}%',
        },
        axisLine: {
          lineStyle: {
            color: '#FF6B6B',
          },
        },
        nameTextStyle: {
          color: '#FF6B6B',
        },
      },
    ],
    series: [
      {
        name: '实际工作时间',
        type: 'bar',
        barWidth: '40%',
        data: actualWorkTimeData,
        itemStyle: {
          color: '#5B9BD5',
        },
        label: {
          show: true,
          position: 'top',
          formatter: '{c}',
          color: '#333',
          fontSize: 11,
          fontWeight: 'normal',
        },
      },
      {
        name: '作业率',
        type: 'line',
        yAxisIndex: 1,
        data: utilizationRateData,
        itemStyle: {
          color: '#FF6B6B',
        },
        lineStyle: {
          color: '#FF6B6B',
          width: 2,
        },
        symbol: 'circle',
        symbolSize: 6,
        label: {
          show: true,
          position: 'top',
          formatter: '{c}%',
          color: '#FF6B6B',
          fontSize: 11,
          fontWeight: 'normal',
        },
      },
    ],
  }
})
</script>
