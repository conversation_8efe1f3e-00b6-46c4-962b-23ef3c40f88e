<template>
  <div class="w-full">
    <BaseEchart v-if="options" :options="options" height="500px" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import BaseEchart from '@/BaseComponent/BaseEchart/src/echart.vue'
import { ElMessage } from 'element-plus'
import { apiDataStatsTunneling02b } from '@/apis/data'
import { ToolUtils } from '@/utils/tool'
import type { TDataTunnelingDepartmentWithPlanStats } from '@/apis/model'

interface Props {
  dateRange: {
    viewType: string
    startDate: string
    endDate: string
  }
}

const props = defineProps<Props>()

// 图表数据
const chartData = ref<TDataTunnelingDepartmentWithPlanStats[]>([])
const lastViewType = ref('daily')

// 获取图表数据
const fetchChartData = async () => {
  try {
    const { viewType, startDate, endDate } = props.dateRange
    if (!startDate || !endDate) {
      return
    }

    const params = {
      viewType,
      startDate,
      endDate
    }

    const res = await apiDataStatsTunneling02b(params)
    chartData.value = res || []
    lastViewType.value = viewType
  } catch (error) {
    console.error('获取重点工程按项目部数据失败:', error)
    ElMessage.error('获取数据失败')
  }
}

// 监听日期范围变化
watch(() => props.dateRange, () => {
  if (props.dateRange?.startDate) {
    fetchChartData()
  }
}, {
  immediate: true,
  deep: true
})

// 格式化日期显示
const formatDisplayDate = (item: any) => ToolUtils.formatDisplayDate(lastViewType.value, item)

// 按日期和项目部分组数据
const groupedData = computed(() => {
  const groups: Record<
    string,
    Record<string, { planTunnelingLength?: any; totalTunnelingLength?: any }>
  > = {}

  chartData.value.forEach((item: TDataTunnelingDepartmentWithPlanStats) => {
    const dateKey = formatDisplayDate(item)

    if (!dateKey) return

    if (!groups[dateKey]) {
      groups[dateKey] = {}
    }

    const department = item.projectDepartmentName || '未知项目部'
    groups[dateKey][department] = {
      planTunnelingLength: item.planTunnelingLength,
      totalTunnelingLength: item.totalTunnelingLength,
    }
  })

  return groups
})

// 图表配置
const options = computed(() => {
  if (!chartData.value || !chartData.value.length) {
    return {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
      },
      xAxis: {
        type: 'category',
        data: [],
      },
      yAxis: {
        type: 'value',
        name: '掘进长度(米)',
      },
      series: [],
    }
  }

  // 获取所有唯一的日期
  const dates = Object.keys(groupedData.value).sort()

  // 获取所有项目部
  const departments = Array.from(
    new Set(
      Object.values(groupedData.value).flatMap((departments) =>
        Object.keys(departments)
      )
    )
  ).sort()

  // 为每个项目部创建两个系列：计划和实际
  const series: any[] = []
  const colors = ['#5B9BD5', '#70AD47', '#FFC000', '#C55A5A', '#9966CC', '#FF9900']

  departments.forEach((department, index) => {
    const planData = dates.map((date) => {
      const departmentData = groupedData.value[date]?.[department]
      return departmentData?.planTunnelingLength || 0
    })

    const actualData = dates.map((date) => {
      const departmentData = groupedData.value[date]?.[department]
      return departmentData?.totalTunnelingLength || 0
    })

    const baseColor = colors[index % colors.length]

    // 计划系列
    series.push({
      name: `${department}-计划`,
      type: 'bar',
      barGap: '10%',
      barCategoryGap: '20%',
      data: planData,
      itemStyle: {
        color: baseColor + '80',
      },
      label: {
        show: true,
        position: 'top',
        formatter: '{c}',
        color: '#333',
        fontSize: 10,
        fontWeight: 'normal',
      },
    })

    // 实际系列
    series.push({
      name: `${department}-实际`,
      type: 'bar',
      barGap: '10%',
      barCategoryGap: '20%',
      data: actualData,
      itemStyle: {
        color: baseColor,
      },
      label: {
        show: true,
        position: 'top',
        formatter: '{c}',
        color: '#333',
        fontSize: 10,
        fontWeight: 'normal',
      },
    })
  })

  // 计算Y轴最大值
  const allValues = series.flatMap(s => s.data)
  const getMax = (arr: number[]) => {
    let max = Math.round(Math.max(...arr))
    if (max === 0) return 100
    return (Number(max.toString()[0]) + 1) * Math.pow(10, max.toString().length - 1)
  }
  const yMax = getMax(allValues)

  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
      formatter: function(params: any) {
        const axisValue = params[0].axisValue;
        let result = axisValue + '<br/>';
        
        // 按项目部分组参数
        const departmentGroups: Record<string, { plan?: any, actual?: any }> = {}
        
        params.forEach((param: any) => {
          const seriesName = param.seriesName
          const match = seriesName.match(/^(.+)-(计划|实际)$/)
          if (match) {
            const department = match[1]
            const type = match[2]
            
            if (!departmentGroups[department]) {
              departmentGroups[department] = {}
            }
            
            if (type === '计划') {
              departmentGroups[department].plan = param
            } else {
              departmentGroups[department].actual = param
            }
          }
        })
        
        // 为每个项目部显示计划、实际和完成率
        Object.entries(departmentGroups).forEach(([department, data]) => {
          if (data.plan || data.actual) {
            result += `<div style="margin:10px 0;padding:5px;border-left:3px solid ${data.actual?.color || data.plan?.color};">
              <strong>${department}</strong><br/>`;
            
            if (data.plan) {
              result += `计划: <strong>${data.plan.value}米</strong><br/>`;
            }
            if (data.actual) {
              result += `实际: <strong>${data.actual.value}米</strong><br/>`;
            }
            
            // 计算完成率
            if (data.plan && data.actual && data.plan.value > 0) {
              const rate = ((data.actual.value / data.plan.value) * 100).toFixed(2);
              result += `完成率: <strong>${rate}%</strong>`;
            }
            
            result += '</div>';
          }
        })
        
        return result;
      }
    },
    legend: {
      data: series.map(s => s.name),
      bottom: 0,
      type: 'scroll',
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      boundaryGap: ['5%', '5%'],
      data: dates,
      axisLabel: {
        rotate: 0,
        margin: 15,
        overflow: 'truncate',
        formatter: (value: string) => {
          if (lastViewType.value === 'daily') {
            return value
          } else if (lastViewType.value === 'monthly') {
            return value.replace('年', '年\n')
          }
          return value
        },
      },
      axisTick: {
        alignWithLabel: true,
      },
    },
    yAxis: {
      type: 'value',
      name: '掘进长度(米)',
      min: 0,
      max: yMax,
      axisLabel: {
        formatter: '{value}',
      },
      axisLine: {
        lineStyle: {
          color: '#5B9BD5',
        },
      },
      nameTextStyle: {
        color: '#5B9BD5',
      },
    },
    series,
  }
})
</script>
