<template>
  <div class="default-main page p-6 bg-white rounded shadow">
    <div class="mb-6">
      <div class="flex justify-between items-center mb-6">
        <h2 class="font-bold text-2xl text-gray-800">设备台效 - {{ equipmentName }}</h2>
        <div class="flex space-x-2">
          <el-button class="!px-4" icon="DataAnalysis">数据分析</el-button>
          <el-button class="!px-4" type="primary" icon="Setting" @click="goDataManagement">数据管理</el-button>
        </div>
      </div>

      <div class="flex flex-wrap items-center justify-between gap-4 mb-6">
        <div class="flex flex-wrap items-center gap-2">
          <el-select 
            v-model="chartType" 
            placeholder="选择图表类型" 
            style="width: 150px"
            class="mr-4"
          >
            <el-option label="台效" value="efficiency" />
            <el-option label="作业率" value="utilization" />
          </el-select>
          <ChartDateRange1 v-model="chartDateRange" />
        </div>
      </div>
    </div>

    <div class="w-full">
      <Efficiency
        v-if="chartType === 'efficiency'" 
        :equipment-type="equipmentType"
        :dateRange="chartDateRange"
      />
      <Utilization
        v-if="chartType === 'utilization'" 
        :equipment-type="equipmentType"
        :dateRange="chartDateRange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import ChartDateRange1 from '@/components/ChartDateRange/ChartDateRange1.vue'
import Efficiency from './components/Efficiency.vue'
import Utilization from './components/Utilization.vue'
import { useRouter } from 'vue-router'
import { equipmentTypeMap } from './config/equipmentTypeMap'

const router = useRouter()

// 设备类型
const equipmentType = 2
const equipmentName = equipmentTypeMap[equipmentType] || ''

const goDataManagement = () => {
  router.push('../type0' + equipmentType)
}


// 图表类型选择
const chartType = ref('efficiency')

// 日期范围
const chartDateRange = ref({
  viewType: 'daily', // daily 或 monthly
  startDate: '',
  endDate: '',
})
</script>
