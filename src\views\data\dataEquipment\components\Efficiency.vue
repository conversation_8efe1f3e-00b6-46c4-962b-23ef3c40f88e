<template>
  <div class="w-full">
    <BaseEchart v-if="options" :options="options" height="500px" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import BaseEchart from '@/BaseComponent/BaseEchart/src/echart.vue'
import { ElMessage } from 'element-plus'
import { apiDataDataEquipmentEfficiency } from '@/apis/data'
import { ToolUtils } from '@/utils/tool'
import type { TDataEquipmentEfficiencyStats } from '@/apis/model'

interface Props {
  equipmentType: number
  dateRange: {
    viewType: string
    startDate: string
    endDate: string
  }
}

const props = defineProps<Props>()

// 图表数据
const chartData = ref<TDataEquipmentEfficiencyStats[]>([])
const lastViewType = ref('daily')

// 获取图表数据
const fetchChartData = async () => {
  try {
    if (!props.dateRange?.viewType || !props.dateRange.startDate || !props.dateRange?.endDate) {
      return
    }

    const params = {
      equipmentType: props.equipmentType,
      startTime: props.dateRange.startDate,
      endTime: props.dateRange.endDate,
      timeType: props.dateRange.viewType
    }

    const res = await apiDataDataEquipmentEfficiency(params)
    chartData.value = res || []
    lastViewType.value = props.dateRange.viewType
  } catch (error) {
    console.error('获取设备台效数据失败:', error)
    ElMessage.error('获取数据失败')
  }
}

// 监听参数变化
watch([() => props.dateRange.viewType], () => {
  fetchChartData()
}, {
  immediate: true,
  deep: true
})

// 格式化日期显示
const formatDisplayDate = (item: TDataEquipmentEfficiencyStats) => {
  if (lastViewType.value === 'daily') {
    return item.startDate || ''
  } else if (lastViewType.value === 'monthly') {
    return `${item.year}年${item.month}月`
  } else if (lastViewType.value === 'weekly') {
    return `${item.year}年第${item.weekNumber}周`
  }
  return item.startDate || ''
}

// 图表配置
const options = computed(() => {
  if (!chartData.value || !chartData.value.length) {
    return {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      xAxis: {
        type: 'category',
        data: []
      },
      yAxis: [
        {
          type: 'value',
          name: '处理量(吨)'
        },
        {
          type: 'value',
          name: '作业时间(小时)',
          position: 'right'
        }
      ],
      series: []
    }
  }

  const xAxisData = chartData.value.map((item: TDataEquipmentEfficiencyStats) => formatDisplayDate(item))
  const processingVolumeData = chartData.value.map((item: TDataEquipmentEfficiencyStats) => item.totalProcessingVolume || 0)
  const operationTimeData = chartData.value.map((item: TDataEquipmentEfficiencyStats) => item.totalOperationTime || 0)
  const efficiencyData = chartData.value.map((item: TDataEquipmentEfficiencyStats) => item.efficiency || 0)

  const getMax = (arr: number[]) => {
    let max = Math.round(Math.max(...arr))
    if (max === 0) return 100
    return (Number(max.toString()[0]) + 1) * Math.pow(10, max.toString().length - 1)
  }
  
  const processingVolumeMax = getMax(processingVolumeData)
  const operationTimeMax = getMax(operationTimeData)

  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: function(params: any) {
        const axisValue = params[0].axisValue;
        let result = axisValue + '<br/>';
        
        // 获取对应数据项的台效值
        const dataIndex = params[0].dataIndex;
        const efficiency = efficiencyData[dataIndex];
        
        // 添加台效信息
        result += `<div style="margin:5px 0 10px 0;color:#666;">
          台效: <strong>${efficiency}吨/小时</strong>
        </div>`;
        
        // 添加处理量和作业时间
        params.forEach((item: any) => {
          const value = item.value;
          const seriesName = item.seriesName;
          const color = item.color;
          const unit = seriesName.includes('处理量') ? '吨' : '小时';

          result += `<div style="display:flex;align-items:center;margin:5px 0;">
            <span style="display:inline-block;width:10px;height:10px;background:${color};border-radius:50%;margin-right:5px;"></span>
            ${seriesName}: <strong>${value}${unit}</strong>
          </div>`;
        });
        
        return result;
      }
    },
    legend: {
      data: ['处理量', '作业时间'],
      bottom: 0,
    },
    grid: {
      left: '3%',
      right: '8%',
      bottom: '10%',
      containLabel: true,
    },
    xAxis: [
      {
        type: 'category',
        boundaryGap: ['5%', '5%'],
        data: xAxisData,
        axisPointer: {
          type: 'shadow',
        },
        axisLabel: {
          rotate: 0,
          margin: 15,
          overflow: 'truncate',
          formatter: (value: string) => {
            if (lastViewType.value === 'monthly') {
              return value.replace('年', '年\n')
            }
            return value
          },
        },
      },
    ],
    yAxis: [
      {
        type: 'value',
        name: '处理量(吨)',
        min: 0,
        max: processingVolumeMax,
        interval: processingVolumeMax / 5,
        axisLabel: {
          formatter: '{value}',
        },
        axisLine: {
          lineStyle: {
            color: '#5B9BD5',
          },
        },
        nameTextStyle: {
          color: '#5B9BD5',
        },
      },
      {
        type: 'value',
        name: '作业时间(小时)',
        min: 0,
        max: operationTimeMax,
        interval: operationTimeMax / 5,
        position: 'right',
        axisLabel: {
          formatter: '{value}',
        },
        axisLine: {
          lineStyle: {
            color: '#70AD47',
          },
        },
        nameTextStyle: {
          color: '#70AD47',
        },
      },
    ],
    series: [
      {
        name: '处理量',
        type: 'bar',
        barWidth: '40%',
        data: processingVolumeData,
        itemStyle: {
          color: '#5B9BD5',
        },
        label: {
          show: true,
          position: 'top',
          formatter: '{c}',
          color: '#333',
          fontSize: 11,
          fontWeight: 'normal',
        },
      },
      {
        name: '作业时间',
        type: 'line',
        yAxisIndex: 1,
        data: operationTimeData,
        itemStyle: {
          color: '#70AD47',
        },
        lineStyle: {
          color: '#70AD47',
          width: 2,
        },
        symbol: 'circle',
        symbolSize: 6,
        label: {
          show: true,
          position: 'top',
          formatter: '{c}',
          color: '#70AD47',
          fontSize: 11,
          fontWeight: 'normal',
        },
      },
    ],
  }
})
</script>
