export const tableItem: BaseTableItem[] = [
  {
    prop: 'planDate',
    label: '计划月份',
  },
  {
    prop: 'rawOreProcessingVolume',
    label: '原矿处理量',
  },
  {
    prop: 'drySeparationVolume',
    label: '干选量',
  },
  {
    prop: 'grindingFeedVolume',
    label: '入磨量',
  },
  {
    prop: 'rawOreGrade',
    label: '原矿品位-TFe',
  },
  {
    prop: 'concentrateGrade',
    label: '精矿品位-TFe',
  },
  {
    prop: 'tailingGrade',
    label: '尾矿品位-TFe',
  },
  {
    prop: 'concentrateVolume',
    label: '精矿量',
  },
  {
    prop: 'todo',
    label: '操作',
    width: '200',
    fixed: !window.isSmallScreen ? 'right' : false,
    slotName: 'todo',
    showOverflowTooltip: false,
  },
  {
    prop: 'concentrateFineness',
    label: '精矿细度(-500目含量)',
  },
  {
    prop: 'ironConcentrateMoisture',
    label: '铁精粉水分',
  },
  {
    prop: 'comprehensiveRatio',
    label: '综合选比',
  },
  {
    prop: 'grindingRatio',
    label: '入磨选比',
  },
  {
    prop: 'tailingsAgitatorTank',
    label: '尾矿搅拌槽-尾矿粒级(-200目)',
  },
  {
    prop: 'overflowOfTailings',
    label: '尾矿脱水筛上量',
  },
  {
    prop: 'rawOreGradeMfe',
    label: '原矿品位-mFe',
  },
  {
    prop: 'tailingGradeMfe',
    label: '尾矿品位-mFe',
  },
]
export default (): BaseTableProps => {
  return {
    tableItem,
    elTableConfig: {
      tooltipOptions: {
        popperClass: 'lmw_popper',
        effect: 'light',
      },
      rowKey: 'id',
    },
    showIndex: false,
    showChoose: true,
    pagination: true,
  }
}
