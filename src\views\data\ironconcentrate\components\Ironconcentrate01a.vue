<template>
  <div class="w-full">
    <BaseEchart v-if="options" :options="options" height="500px" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import BaseEchart from '@/BaseComponent/BaseEchart/src/echart.vue'
import { ElMessage } from 'element-plus'
import { apiDataStatsIronconcentrate01a } from '@/apis/data'
import { ToolUtils } from '@/utils/tool'
import type { TDataIronConcentrateTotalWithPlanStats } from '@/apis/model'

interface Props {
  dateRange: {
    viewType: string
    startDate: string
    endDate: string
  }
}

const props = defineProps<Props>()

// 图表数据
const chartData = ref<TDataIronConcentrateTotalWithPlanStats[]>([])
const lastViewType = ref('daily')

// 获取图表数据
const fetchChartData = async () => {
  try {
    const { viewType, startDate, endDate } = props.dateRange
    if (!startDate || !endDate) {
      return
    }

    const params = {
      viewType,
      startDate,
      endDate
    }

    const res = await apiDataStatsIronconcentrate01a(params)
    chartData.value = res || []
    lastViewType.value = viewType
  } catch (error) {
    console.error('获取铁精矿整体数据失败:', error)
    ElMessage.error('获取数据失败')
  }
}

// 监听日期范围变化
watch(() => props.dateRange, () => {
  if (props.dateRange?.startDate) {
    fetchChartData()
  }
}, {
  immediate: true,
  deep: true
})

// 格式化日期显示
const formatDisplayDate = (item: TDataIronConcentrateTotalWithPlanStats) => ToolUtils.formatDisplayDate(lastViewType.value, item)

// 图表配置
const options = computed(() => {
  if (!chartData.value || !chartData.value.length) {
    return {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      xAxis: {
        type: 'category',
        data: []
      },
      yAxis: {
        type: 'value'
      },
      series: []
    }
  }

  const xAxisData = chartData.value.map((item: TDataIronConcentrateTotalWithPlanStats) => formatDisplayDate(item))
  const planData = chartData.value.map((item: TDataIronConcentrateTotalWithPlanStats) => item.planProductionVolume || 0)
  const actualData = chartData.value.map((item: TDataIronConcentrateTotalWithPlanStats) => item.totalProductionVolume || 0)
  
  // 计算完成率
  const completionRateData = chartData.value.map((item: TDataIronConcentrateTotalWithPlanStats) => {
    const plan = item.planProductionVolume || 0
    const actual = item.totalProductionVolume || 0
    return plan > 0 ? Math.round((actual / plan) * 100 * 100) / 100 : 0
  })

  const getMax = (arr: number[]) => {
    let max = Math.round(Math.max(...arr))
    if (max === 0) return 100
    return (Number(max.toString()[0]) + 1) * Math.pow(10, max.toString().length - 1)
  }
  const yMax = getMax([...planData, ...actualData])
  const completionRateMax = Math.max(120, Math.max(...completionRateData) + 20)

  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: function(params: any) {
        const axisValue = params[0].axisValue;
        let result = axisValue + '<br/>';
        
        // 获取计划和实际值的索引
        const planIndex = params.findIndex((p: any) => p.seriesName === '计划铁精矿');
        const actualIndex = params.findIndex((p: any) => p.seriesName === '实际铁精矿');
        
        // 计算完成率
        let completionRate = '';
        if (planIndex !== -1 && actualIndex !== -1) {
          const planValue = params[planIndex].value;
          const actualValue = params[actualIndex].value;
          if (planValue > 0) {
            const rate = (actualValue / planValue * 100).toFixed(2);
            completionRate = `<div style="margin:5px 0 10px 0;color:#666;">
              完成率: <strong>${rate}%</strong>
            </div>`;
          }
        }
        
        // 添加完成率
        result += completionRate;
        
        // 添加计划和实际值
        params.forEach((item: any) => {
          const value = item.value;
          const seriesName = item.seriesName;
          const color = item.color;

          result += `<div style="display:flex;align-items:center;margin:5px 0;">
            <span style="display:inline-block;width:10px;height:10px;background:${color};border-radius:50%;margin-right:5px;"></span>
            ${seriesName}: <strong>${value}吨</strong>
          </div>`;
        });
        return result;
      }
    },
    legend: {
      data: ['计划铁精矿', '实际铁精矿', '完成率'],
      bottom: 0,
    },
    grid: {
      left: '3%',
      right: '8%',
      bottom: '10%',
      containLabel: true,
    },
    xAxis: [
      {
        type: 'category',
        boundaryGap: ['5%', '5%'],
        data: xAxisData,
        axisPointer: {
          type: 'shadow',
        },
        axisLabel: {
          rotate: 0,
          margin: 15,
          overflow: 'truncate',
          formatter: (value: string) => {
            if (lastViewType.value === 'daily') {
              return value
            } else if (lastViewType.value === 'monthly') {
              return value.replace('年', '年\n')
            }
            return value
          },
        },
      },
    ],
    yAxis: [
      {
        type: 'value',
        name: '铁精矿(吨)',
        min: 0,
        max: yMax,
        interval: yMax / 5,
        axisLabel: {
          formatter: '{value}',
        },
        axisLine: {
          lineStyle: {
            color: '#5B9BD5',
          },
        },
        nameTextStyle: {
          color: '#5B9BD5',
        },
      },
      {
        type: 'value',
        name: '完成率(%)',
        min: 0,
        max: completionRateMax,
        interval: completionRateMax / 5,
        position: 'right',
        axisLabel: {
          formatter: '{value}%',
        },
        axisLine: {
          lineStyle: {
            color: '#FF6B6B',
          },
        },
        nameTextStyle: {
          color: '#FF6B6B',
        },
      },
    ],
    series: [
      {
        name: '计划铁精矿',
        type: 'bar',
        barWidth: '30%',
        data: planData,
        itemStyle: {
          color: '#5B9BD580',
        },
        label: {
          show: true,
          position: 'top',
          formatter: '{c}',
          color: '#333',
          fontSize: 11,
          fontWeight: 'normal',
        },
      },
      {
        name: '实际铁精矿',
        type: 'bar',
        barWidth: '30%',
        data: actualData,
        itemStyle: {
          color: '#5B9BD5',
        },
        label: {
          show: true,
          position: 'top',
          formatter: '{c}',
          color: '#333',
          fontSize: 11,
          fontWeight: 'normal',
        },
      },
      {
        name: '完成率',
        type: 'line',
        yAxisIndex: 1,
        data: completionRateData,
        itemStyle: {
          color: '#FF6B6B',
        },
        lineStyle: {
          color: '#FF6B6B',
          width: 2,
        },
        symbol: 'circle',
        symbolSize: 6,
        label: {
          show: true,
          position: 'top',
          formatter: '{c}%',
          color: '#FF6B6B',
          fontSize: 11,
          fontWeight: 'normal',
        },
      },
    ],
  }
})
</script>
