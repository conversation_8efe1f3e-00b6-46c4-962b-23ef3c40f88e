export const tableItem: BaseTableItem[] = [
  {
    prop: 'id',
    label: '铁精粉数据ID',
    width: '80',
  },

  {
    prop: 'operationDate',
    label: '作业日期'
  },
  {
    prop: 'tfeContent',
    label: 'TFe含量'
  },
  {
    prop: 'finenessMinus500',
    label: '精矿细度-500目含量'
  },
  {
    prop: 'productionVolume',
    label: '产量'
  },
  {
    prop: 'moistureContent',
    label: '水分'
  },
  {
    prop: 'remarks',
    label: '备注'
  },
  {
    prop: 'todo',
    label: '操作',
    width: '200',
    fixed: !window.isSmallScreen ? 'right' : false,
    slotName: 'todo',
    showOverflowTooltip: false,
  },

]
export default (): BaseTableProps => {
  return {
    tableItem,
    elTableConfig: {
      tooltipOptions: {
        popperClass: 'lmw_popper',
        effect: 'light',
      },
      rowKey: 'id',
    },
    showIndex: false,
    showChoose: true,
    pagination: true,
  }
}
