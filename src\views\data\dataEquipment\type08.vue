<script setup name="DataEquipmentLocomotive" lang="ts">
import { dataBaseUrl } from '@/api/config/base.js'
import { DictOptions, getEquipmentList, getPeriodList } from '@/dict'
import getComputedConfig from '@/hooks/getPageConfig'
import useDialog from '@/hooks/useDialog'
import { proxy } from '@/utils/provide'
import BatchAddDialog from './components/BatchAddDialog.vue'
import getContentConfig from './config/contentConfig'
import getDialogConfig from './config/dialogConfig'
import getSearchConfig from './config/searchConfig'
import ExcelStepImport from '@/components/ExcelStepImport'
import { equipmentTypeMap } from './config/equipmentTypeMap'
import { TableItem } from '@/BaseComponent/BaseTable'
import { FormItem } from '@/BaseComponent/BaseForm'

const equipmentType = 8
const equipmentTypeName = equipmentTypeMap[equipmentType.toString()] || ''
const contentFields = [
  'id', // 设备数据ID
  'equipmentNo', // 设备编号
  // 'mineCarsNumber', // 矿车数量
  // 'numberOfRunningTrains', // 运行列次
  // 'operationTime', // 运行时长 equipmentType = 1 时隐藏
  'totalProcessingVolume', // 总处理量
  'operationDate', // 运行日期
  'startTime', // 运行开始时间
  'endTime', // 运行结束时间
  'todo', // 操作
]
const dialogFields = [
  'equipmentNo', // 设备编号,
  // 'mineCarsNumber', // 矿车数量,
  // 'numberOfRunningTrains', // 运行列次,
  'operationDate', // 运行日期,
  'startTime', // 运行开始时间,
  'endTime', // 运行结束时间,
  'totalProcessingVolume', // 总处理量,
]

const pageName = 'dataEquipment'
const requestBaseUrl = dataBaseUrl
const pageSearchRef = useTemplateRef('pageSearchRef')
const pageContentRef = useTemplateRef('pageContentRef')
const dialogHideItems = ref<string[]>([])
const tableHideItems = ref<string[]>([])
const periodOptions = ref<DictOptions[]>([])
const equipmentOptions = ref<DictOptions[]>([])

const initDict = async () => {
  periodOptions.value = await getPeriodList()
  // 获取对应设备类型的设备编号列表
  // await fetchEquipmentOptions()
   equipmentOptions.value = await getEquipmentList(equipmentType)
}

initDict()

const dictMap = ref({
  workingPeriodId: periodOptions,
  equipmentNo: equipmentOptions,
})

const searchConfig = getSearchConfig()
const searchConfigComputed = computed(() => {
  return getComputedConfig(searchConfig, dictMap)
})

const tableSelected = ref<any[]>([])
const tableListener = {
  selectionChange: (selected: any) => {
    tableSelected.value = selected
  },
}

const contentConfig = getContentConfig()
const contentConfigComputed = computed(() => {
  // 电机车显示所有字段，但隐藏operationTime和equipmentType
  const customHideItems = contentConfig.tableItem.filter((item: TableItem) => !contentFields.includes(item.prop)).map(item => item.prop)
  const hideItems = [...tableHideItems.value, ...customHideItems]
  contentConfig.hideItems = hideItems
  return contentConfig
})

const dialogConfig = getDialogConfig()
const dialogConfigComputed = computed(() => {
  // 电机车显示所有字段，但隐藏operationTime和equipmentType
  const customHideItems = dialogConfig.formItems.filter((item: FormItem) => !dialogFields.includes(item.field)).map(item => item.field)
  const hideItems = [...dialogHideItems.value, ...customHideItems]
  dialogConfig.hideItems = hideItems
  return getComputedConfig(dialogConfig, dictMap)
})

const addCallBack = () => {
  dialogHideItems.value.length = 0
  // 设置默认的设备类型
  nextTick(() => {
    dialogRef.value?.setFormData('equipmentType', equipmentType)
  })
}

const editCallBack = (_item: any, type: any, _res: any) => {
  isEditMore.value = type
}

const isEditMore = ref(false)
const editMoreClick = () => {
  if (tableSelected.value.length > 0) {
    const data = tableSelected.value[0]
    pageContentRef.value?.editClick(data, true)
    nextTick(() => {
      const newArray = tableSelected.value.slice(1)
      dialogRef.value?.changeSelected(newArray)
    })
  }
}

const editNext = (data: any) => {
  pageContentRef.value?.editClick(data, true)
}

const { dialogRef, infoInit, addClick, editBtnClick } = useDialog(
  addCallBack,
  editCallBack,
  '添加'
)

const dialogWidth = ref('700px')
const searchData = computed(() => {
  return pageContentRef.value?.finalSearchData
})

const search = () => {
  pageSearchRef.value?.search()
}

const beforeSend = (queryInfo: anyObj) => {
  // 添加设备类型参数
  queryInfo.equipmentType = equipmentType
  
  // Process 运行日期 date range
  if (queryInfo.operationDate && Array.isArray(queryInfo.operationDate)) {
    const dateRange = queryInfo.operationDate
    queryInfo['params[beginOperationDate]'] = dateRange[0]
    queryInfo['params[endOperationDate]'] = dateRange[1]
    delete queryInfo.operationDate
  }
  // Process 运行开始时间 date range
  if (queryInfo.startTime && Array.isArray(queryInfo.startTime)) {
    const dateRange = queryInfo.startTime
    queryInfo['params[beginStartTime]'] = dateRange[0]
    queryInfo['params[endStartTime]'] = dateRange[1]
    delete queryInfo.startTime
  }
  // Process 运行结束时间 date range
  if (queryInfo.endTime && Array.isArray(queryInfo.endTime)) {
    const dateRange = queryInfo.endTime
    queryInfo['params[beginEndTime]'] = dateRange[0]
    queryInfo['params[endEndTime]'] = dateRange[1]
    delete queryInfo.endTime
  }
}

const permission = ref({
  add: 'data:dataEquipment:add',
  edit: 'data:dataEquipment:edit',
  del: 'data:dataEquipment:remove',
})

// Batch add dialog control
const batchAddVisible = ref(false)
const handleBatchAdd = () => {
  batchAddVisible.value = true
}

const handleBatchAddRefresh = () => {
  search()
}

const onChangeShowColumn = (filterArr: string[]) => {
  tableHideItems.value = filterArr
}

// Excel import dialog control
const importVisible = ref(false)
const handleImport = () => {
  importVisible.value = true
}

const handleImportSuccess = () => {
  search()
}

const handleImportError = (error: any) => {
  console.error('Import error:', error)
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy.download(
    'data/dataEquipment/export',
    {
      ...searchData.value,
      equipmentType,
    },
    `${equipmentTypeName}数据_${new Date().getTime()}.xlsx`
  )
}
</script>

<template>
  <div class="default-main page">
    <!-- 页面标题 -->
    <div class="mb-4 p-4 bg-white rounded shadow">
      <h2 class="text-xl font-semibold text-gray-800">{{ equipmentTypeName }}数据管理</h2>
    </div>

    <PageSearch
      ref="pageSearchRef"
      :pageName="pageName"
      :searchConfig="searchConfigComputed"
    ></PageSearch>
    <PageContent
      ref="pageContentRef"
      :pageName="pageName"
      :contentConfig="contentConfigComputed"
      :dictMap="dictMap"
      :tableListener="tableListener"
      :tableSelected="tableSelected"
      :permission="permission"
      :requestBaseUrl="requestBaseUrl"
      @beforeSend="beforeSend"
      @addClick="addClick"
      @editBtnClick="editBtnClick"
      @onChangeShowColumn="onChangeShowColumn"
      @editMoreClick="editMoreClick"
    >
      <template #handleLeft>
        <el-button
          v-hasPermi="[permission.add]"
          type="primary"
          @click="handleBatchAdd"
        >
          <SvgIcon :size="14" iconClass="plus"></SvgIcon>
          <span class="ml6">按日添加</span>
        </el-button>
        <el-button
          class="order16"
          type="success"
          v-hasPermi="['data:dataEquipment:add']"
          @click="handleImport"
        >
          <SvgIcon size="14" iconClass="upload" />
          <span class="ml6">导入</span>
        </el-button>
        <el-button
          class="order17"
          type="warning"
          v-hasPermi="['data:dataEquipment:export']"
          @click="handleExport"
        >
          <SvgIcon size="14" iconClass="download" />
          <span class="ml6">导出</span>
        </el-button>
      </template>
    </PageContent>
    <PageDialog
      ref="dialogRef"
      sendIdKey="id"
      :width="getWidth(dialogWidth)"
      :pageName="pageName"
      :dialogConfig="dialogConfigComputed"
      :infoInit="infoInit"
      :search="search"
      :isEditMore="isEditMore"
      :requestBaseUrl="requestBaseUrl"
      @editNext="editNext"
      :otherInfo="{ equipmentType }"
    >
      <template #startTimeCustom="{ backData }">
        <el-time-picker
          v-model="backData.formData.startTime"
          placeholder="请选择运行开始时间"
          format="HH:mm"
          value-format="HH:mm:00"
          style="width: 100%"
        />
      </template>
      <template #endTimeCustom="{ backData }">
        <el-time-picker
          v-model="backData.formData.endTime"
          placeholder="请选择运行结束时间"
          format="HH:mm"
          value-format="HH:mm:00"
          style="width: 100%"
        />
      </template>
    </PageDialog>
    <BatchAddDialog
      v-model="batchAddVisible"
      :equipmentType="equipmentType"
      :equipmentTypeName="equipmentTypeName"
      :periodOptions="periodOptions"
      :equipmentOptions="equipmentOptions"
      @refresh="handleBatchAddRefresh"
    />
    <ExcelStepImport
      v-model:visible="importVisible"
      :templateKey="`data_equipment_${equipmentType}`"
      :title="`${equipmentTypeName}数据导入`"
      @success="handleImportSuccess"
      @error="handleImportError"
    />
  </div>
</template>

<style scoped lang="scss"></style>
