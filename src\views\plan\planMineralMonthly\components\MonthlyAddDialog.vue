<script setup lang="ts">
import { planBaseUrl } from '@/api/config/base.js'
import { request } from '@/utils/hsj/service/index'
import { Delete, Plus } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { computed, ref, watch } from 'vue'
import { apiPlanPlanMineralMonthlyBatch } from '@/apis/plan'
import type { TPlanMineralMonthlyBatchDto } from '@/apis/model.d'

const pageName = 'planMineralMonthly'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['update:modelValue', 'refresh'])

const dialogVisible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val),
})

const selectedMonth = ref('')

const tableData = ref<any[]>([])
const loading = ref(false)

const formRef = ref<any>(null)
const rules = {
  selectedMonth: [{ required: true, message: '请选择月份', trigger: 'change' }],
}

const fetchPlans = async () => {
  if (!selectedMonth.value) {
    ElMessage.warning('请先选择月份')
    return
  }

  loading.value = true
  try {
    const res = await request<any>({
      url: `${planBaseUrl}/${pageName}/list`,
      method: 'get',
      params: {
        planDate: selectedMonth.value,
        pageSize: 999,
      },
    })

    if (res.code === 200) {
      tableData.value = res.rows || []
    } else {
      ElMessage.error(res.msg || '获取数据失败')
    }
  } catch (error) {
    console.error('获取计划数据失败:', error)
    ElMessage.error('获取计划数据失败')
  } finally {
    loading.value = false
  }
}

const addRow = () => {
  if (!selectedMonth.value) {
    ElMessage.warning('请先选择月份')
    return
  }

  tableData.value.push({
    planDate: selectedMonth.value,
    rawOreProcessingVolume: '',
    drySeparationVolume: '',
    grindingFeedVolume: '',
    rawOreGrade: '',
    concentrateGrade: '',
    tailingGrade: '',
    concentrateVolume: '',
    concentrateFineness: '',
    ironConcentrateMoisture: '',
    comprehensiveRatio: '',
    grindingRatio: '',
    tailingsAgitatorTank: '',
    overflowOfTailings: '',
    rawOreGradeMfe: '',
    tailingGradeMfe: '',
    isNew: true,
  })

  setTimeout(() => {
    const inputElements = document.querySelectorAll('.monthly-add-dialog .el-table .el-input__inner')
    if (inputElements && inputElements.length > 0) {
      const targetInput = inputElements[inputElements.length - 1]
      if (targetInput) {
        (targetInput as HTMLElement).focus()
      }
    }
  }, 100)
}

const deleteRow = (index: number, _row: any) => {
  tableData.value.splice(index, 1)
}

const handleDialogClosed = () => {
  selectedMonth.value = ''
  tableData.value = []
}

const saveAll = async () => {
  if (!selectedMonth.value) {
    ElMessage.warning('请先选择月份')
    return
  }

  if (tableData.value.length === 0) {
    ElMessage.warning('没有数据需要保存')
    return
  }

  console.log('提交数据:', JSON.stringify(tableData.value, null, 2))

  loading.value = true
  try {
    // 准备提交数据
    const batchData: TPlanMineralMonthlyBatchDto[] = tableData.value.map((item) => ({
      id: item.id,
      planMonth: selectedMonth.value,
      oreQuantity: item.rawOreProcessingVolume,
      oreGrade: item.rawOreGrade,
      goldOutput: item.concentrateVolume,
      crushingQuantity: item.drySeparationVolume,
      grindingQuantity: item.grindingFeedVolume,
      flotationQuantity: item.tailingGrade,
      concentrateFineness: item.concentrateFineness,
      ironConcentrateMoisture: item.ironConcentrateMoisture,
      comprehensiveRatio: item.comprehensiveRatio,
      grindingRatio: item.grindingRatio,
      tailingsAgitatorTank: item.tailingsAgitatorTank,
      overflowOfTailings: item.overflowOfTailings,
      rawOreGradeMfe: item.rawOreGradeMfe,
      tailingGradeMfe: item.tailingGradeMfe,
      operationType: item.isNew ? 'add' : 'edit',
      remark: item.remark || ''
    }))

    // 使用批量保存API
    await apiPlanPlanMineralMonthlyBatch(batchData)

    ElMessage.success('保存成功')
    dialogVisible.value = false
    emit('refresh')
  } catch (error) {
    console.error('保存数据失败:', error)
    ElMessage.error('保存数据失败')
  } finally {
    loading.value = false
  }
}

watch(selectedMonth, (newVal) => {
  if (newVal) {
    tableData.value = []
    fetchPlans()
  }
})
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    title="按月添加选矿计划"
    destroy-on-close
    width="1400px"
    :close-on-click-modal="false"
    append-to-body
    class="monthly-add-dialog"
    @closed="handleDialogClosed"
  >
    <el-form
      ref="formRef"
      :model="{ selectedMonth }"
      :rules="rules"
      label-width="80px"
    >
      <el-form-item label="选择月份" prop="selectedMonth">
        <el-date-picker
          v-model="selectedMonth"
          type="month"
          placeholder="请选择月份"
          format="YYYY-MM"
          value-format="YYYYMM"
          style="width: 100%"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="success" @click="addRow">
          <el-icon><component :is="Plus" /></el-icon> 添加行
        </el-button>
      </el-form-item>
    </el-form>

    <el-table
      v-loading="loading"
      :data="tableData"
      border
      style="width: 100%"
      max-height="600px"
    >
      <el-table-column label="原矿处理量" prop="rawOreProcessingVolume">
        <template #default="{ row }">
          <el-input v-model="row.rawOreProcessingVolume" placeholder="请输入原矿处理量" />
        </template>
      </el-table-column>

      <el-table-column label="干选量" prop="drySeparationVolume">
        <template #default="{ row }">
          <el-input v-model="row.drySeparationVolume" placeholder="请输入干选量" />
        </template>
      </el-table-column>

      <el-table-column label="入磨量" prop="grindingFeedVolume">
        <template #default="{ row }">
          <el-input v-model="row.grindingFeedVolume" placeholder="请输入入磨量" />
        </template>
      </el-table-column>

      <el-table-column label="原矿品位-TFe" prop="rawOreGrade">
        <template #default="{ row }">
          <el-input v-model="row.rawOreGrade" placeholder="请输入原矿品位" />
        </template>
      </el-table-column>

      <el-table-column label="精矿品位-TFe" prop="concentrateGrade">
        <template #default="{ row }">
          <el-input v-model="row.concentrateGrade" placeholder="请输入精矿品位" />
        </template>
      </el-table-column>

      <el-table-column label="尾矿品位-TFe" prop="tailingGrade">
        <template #default="{ row }">
          <el-input v-model="row.tailingGrade" placeholder="请输入尾矿品位" />
        </template>
      </el-table-column>

      <el-table-column label="精矿量" prop="concentrateVolume">
        <template #default="{ row }">
          <el-input v-model="row.concentrateVolume" placeholder="请输入精矿量" />
        </template>
      </el-table-column>

      <el-table-column label="精矿细度(-500目含量)" prop="concentrateFineness">
        <template #default="{ row }">
          <el-input v-model="row.concentrateFineness" placeholder="请输入精矿细度(-500目含量)" />
        </template>
      </el-table-column>
      <el-table-column label="铁精粉水分" prop="ironConcentrateMoisture">
        <template #default="{ row }">
          <el-input v-model="row.ironConcentrateMoisture" placeholder="请输入铁精粉水分" />
        </template>
      </el-table-column>
      <el-table-column label="综合选比" prop="comprehensiveRatio">
        <template #default="{ row }">
          <el-input v-model="row.comprehensiveRatio" placeholder="请输入综合选比" />
        </template>
      </el-table-column>
      <el-table-column label="入磨选比" prop="grindingRatio">
        <template #default="{ row }">
          <el-input v-model="row.grindingRatio" placeholder="请输入入磨选比" />
        </template>
      </el-table-column>
      <el-table-column label="尾矿搅拌槽-尾矿粒级(-200目)" prop="tailingsAgitatorTank">
        <template #default="{ row }">
          <el-input v-model="row.tailingsAgitatorTank" placeholder="请输入尾矿搅拌槽-尾矿粒级(-200目)" />
        </template>
      </el-table-column>
      <el-table-column label="尾矿脱水筛上量" prop="overflowOfTailings">
        <template #default="{ row }">
          <el-input v-model="row.overflowOfTailings" placeholder="请输入尾矿脱水筛上量" />
        </template>
      </el-table-column>
      <el-table-column label="原矿品位-mFe" prop="rawOreGradeMfe">
        <template #default="{ row }">
          <el-input v-model="row.rawOreGradeMfe" placeholder="请输入原矿品位-mFe" />
        </template>
      </el-table-column>
      <el-table-column label="尾矿品位-mFe" prop="tailingGradeMfe">
        <template #default="{ row }">
          <el-input v-model="row.tailingGradeMfe" placeholder="请输入尾矿品位-mFe" />
        </template>
      </el-table-column>

      <el-table-column label="操作" width="120">
        <template #default="{ row, $index }">
          <el-button type="danger" link @click="deleteRow($index, row)">
            <el-icon><component :is="Delete" /></el-icon> 删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <template #footer>
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button type="primary" @click="saveAll" :loading="loading"
        >保 存</el-button
      >
    </template>
  </el-dialog>
</template>

<style>
.monthly-add-dialog .el-dialog__body {
  padding: 20px !important;
}

.monthly-add-dialog .el-table {
  margin-top: 15px;
  width: 100% !important;
}

.monthly-add-dialog .el-dialog__header,
.monthly-add-dialog .el-dialog__footer {
  padding: 15px 20px !important;
}

.monthly-add-dialog .el-dialog__body .el-table__body-wrapper {
  overflow-y: auto;
}

.monthly-add-dialog .el-form-item {
  margin-bottom: 18px;
}
</style>
