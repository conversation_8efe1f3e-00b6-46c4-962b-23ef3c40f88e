<script setup name="Support" lang="ts">
import getSearchConfig from './config/searchConfig'
import getContentConfig from './config/contentConfig'
import getDialogConfig from './config/dialogConfig'
import useDialog from '@/hooks/useDialog'
import getComputedConfig from '@/hooks/getPageConfig'
import { dataBaseUrl } from '@/api/config/base.js'
import { DictOptions, getFaceList, getPeriodList, getProjectDepartmentList, getStopeList } from '@/dict'
import BatchAddDialog from './components/BatchAddDialog.vue'
import ExcelStepImport from '@/components/ExcelStepImport'


const pageName = 'support'
const requestBaseUrl = dataBaseUrl
const pageSearchRef = useTemplateRef('pageSearchRef')
const pageContentRef = useTemplateRef('pageContentRef')
const dialogHideItems = ref<string[]>([])
const tableHideItems = ref<string[]>(['supportType']) // 隐藏支护类型列
const periodOptions = ref<DictOptions[]>([])
const projectOptions = ref<DictOptions[]>([])
const stopeOptions = ref<DictOptions[]>([])
const faceOptions = ref<DictOptions[]>([])


const initDict = async () => {
  periodOptions.value = await getPeriodList()
  projectOptions.value = await getProjectDepartmentList()
  stopeOptions.value = await getStopeList()
  faceOptions.value = await getFaceList()
}
initDict()

const dictMap = ref({
  workingPeriodId: periodOptions,
  projectDepartmentId: projectOptions,
  stopeId: stopeOptions,
  workingFaceId: faceOptions,
})

const searchConfig = getSearchConfig()
const searchConfigComputed = computed(() => {
  return getComputedConfig(searchConfig, dictMap)
})
const tableSelected = ref<any[]>([])
const tableListener = {
  selectionChange: (selected:any) => {
    tableSelected.value = selected
  },
}
const contentConfig = getContentConfig()
const contentConfigComputed = computed(() => {
  contentConfig.hideItems = tableHideItems
  return contentConfig
})

const dialogConfig = getDialogConfig()

const dialogConfigComputed = computed(() => {
  dialogConfig.hideItems = dialogHideItems
  return getComputedConfig(dialogConfig, dictMap)
})

const addCallBack = () => {
  dialogHideItems.value.length = 0
}
const editCallBack = (_item: any, type: any, _res: any) => {
  isEditMore.value = type
}
const isEditMore = ref(false)
const editMoreClick = () => {
  if (tableSelected.value.length > 0) {
    const data = tableSelected.value[0]
    pageContentRef.value?.editClick(data, true)
    nextTick(() => {
      const newArray = tableSelected.value.slice(1)
      dialogRef.value?.changeSelected(newArray)
    })
  }
}

const editNext = (data: any) => {
  pageContentRef.value?.editClick(data, true)
}

const {dialogRef, infoInit, addClick, editBtnClick} = useDialog(
  addCallBack,
  editCallBack,
  '添加'
)

const dialogWidth = ref('700px')
// const searchData = computed(() => {
//   return pageContentRef.value?.finalSearchData
// })

const search = () => {
  pageSearchRef.value?.search()
}

const beforeSend = (queryInfo: anyObj) => {
  // Process 作业日期 date range
  if (queryInfo.operationDate && Array.isArray(queryInfo.operationDate)) {
    const dateRange = queryInfo.operationDate
    queryInfo['params[beginOperationDate]'] = dateRange[0]
    queryInfo['params[endOperationDate]'] = dateRange[1]
    delete queryInfo.operationDate
  }
  // 添加支护类型过滤条件为
  queryInfo['supportType'] = '2'
}

const permission = ref({
  add: 'data:support:add',
  edit: 'data:support:edit',
  del: 'data:support:remove',
})

const onChangeShowColumn = (filterArr: string[]) => {
  tableHideItems.value = filterArr
}

// Batch add dialog control
const batchAddVisible = ref(false)
const handleBatchAdd = () => {
  batchAddVisible.value = true
}

const handleBatchAddRefresh = () => {
  search()
}

// Import/Export functionality
const importVisible = ref(false)

const handleImport = () => {
  importVisible.value = true
}

const handleExport = () => {
  // TODO: 实现导出逻辑
  console.log('导出功能待实现')
}

const handleImportSuccess = () => {
  search()
}

const handleImportError = () => {
  // Handle import error
}
</script>
<template>
  <div class="default-main page">
    <PageSearch
      ref="pageSearchRef"
      :pageName="pageName"
      :searchConfig="searchConfigComputed"
    ></PageSearch>
    <PageContent
      ref="pageContentRef"
      :pageName="pageName"
      :contentConfig="contentConfigComputed"
      :dictMap="dictMap"
      :tableListener="tableListener"
      :tableSelected="tableSelected"
      :permission="permission"
      :requestBaseUrl="requestBaseUrl"
      @beforeSend="beforeSend"
      @addClick="addClick"
      @editBtnClick="editBtnClick"
      @onChangeShowColumn="onChangeShowColumn"
      @editMoreClick="editMoreClick"
    >
      <template #handleLeft>
        <el-button
          v-hasPermi="[permission.add]"
          type="primary"
          @click="handleBatchAdd"
        >
          <SvgIcon :size="14" iconClass="plus"></SvgIcon>
          <span class="ml6">按日添加</span>
        </el-button>
        <el-button
          class="order16"
          type="success"
          v-hasPermi="['data:support:add']"
          @click="handleImport"
        >
          <SvgIcon size="14" iconClass="upload" />
          <span class="ml6">导入</span>
        </el-button>
        <el-button
          class="order17"
          type="warning"
          v-hasPermi="['data:support:export']"
          @click="handleExport"
        >
          <SvgIcon size="14" iconClass="download" />
          <span class="ml6">导出</span>
        </el-button>
      </template>
    </PageContent>
    <PageDialog
      ref="dialogRef"
      sendIdKey="id"
      :width="getWidth(dialogWidth)"
      :pageName="pageName"
      :dialogConfig="dialogConfigComputed"
      :infoInit="infoInit"
      :search="search"
      :isEditMore="isEditMore"
      :requestBaseUrl="requestBaseUrl"
      @editNext="editNext"
      :otherInfo="{ supportType: '2' }"
    />
    <BatchAddDialog
      v-model="batchAddVisible"
      :periodOptions="periodOptions"
      :projectOptions="projectOptions"
      :faceOptions="faceOptions"
      :stopeOptions="stopeOptions"
      @refresh="handleBatchAddRefresh"
    />
    <ExcelStepImport
      v-model:visible="importVisible"
      templateKey="data_support_boltmesh"
      title="锚网支护数据导入"
      @success="handleImportSuccess"
      @error="handleImportError"
    />
  </div>
</template>

<style scoped lang="scss">
</style>
