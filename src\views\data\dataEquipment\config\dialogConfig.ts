export default (): BaseFormProps => {
  return {
    itemStyle: {
      padding: '0px 0px 0px 0px',
    },
    rules: {
      operationDate: [
        { required: true, message: '运行日期不能为空', trigger: 'blur' },
      ],
    },
    formItems: [
      {
        field: 'equipmentNo',
        type: 'select',
        options: [],
        label: '设备编号',
      },
      {
        field: 'mineCarsNumber',
        type: 'input',
        label: '编组数量',
      },
      {
        field: 'numberOfRunningTrains',
        type: 'input',
        label: '运行列次',
      },
      {
        field: 'operationDate',
        label: '运行日期',
        config: {
          clearable: false,
          type: 'date',
          disabledDate: (time: Date) => {
            return time.getTime() > Date.now()
          },
        },
        type: 'datepicker',
      },
      {
        field: 'startTime',
        type: 'custom',
        slotNames: ['startTime'],
        label: '运行开始时间',
      },
      {
        field: 'endTime',
        type: 'custom',
        slotNames: ['endTime'],
        label: '运行结束时间',
      },
      {
        field: 'totalProcessingVolume',
        type: 'input',
        label: '总处理量',
      },
    ],
    colLayout: {
      xl: 24,
      lg: 24,
      md: 24,
      sm: 24,
      xs: 24,
    },
  }
}
