// 自动生成的 API 文件，请勿手动修改

import { request, blob } from '@/utils/hsj/service/index'
import { AxiosResponse } from 'axios'
import { TBaseStope, TBaseProjectDepartment, TBaseWorkingPeriod, TBaseOrePass, TBaseWorkingFace, TBasePriorityProject, TBaseEquipment, TBasePriorityProjectVo } from './model'

export async function apiBaseStopeExport(query: TBaseStope): Promise<AxiosResponse<Blob>> {
  return blob({
    url: '/base/stope/export',
    method: 'post',
    params: query
  }); 
}


export async function apiBaseProjectDepartmentExport(query: TBaseProjectDepartment): Promise<AxiosResponse<Blob>> {
  return blob({
    url: '/base/projectDepartment/export',
    method: 'post',
    params: query
  }); 
}


export async function apiBasePeriodExport(query: TBaseWorkingPeriod): Promise<AxiosResponse<Blob>> {
  return blob({
    url: '/base/period/export',
    method: 'post',
    params: query
  }); 
}


export async function apiBaseOrePassExport(query: TBaseOrePass): Promise<AxiosResponse<Blob>> {
  return blob({
    url: '/base/orePass/export',
    method: 'post',
    params: query
  }); 
}


export async function apiBaseFaceExport(query: TBaseWorkingFace): Promise<AxiosResponse<Blob>> {
  return blob({
    url: '/base/face/export',
    method: 'post',
    params: query
  }); 
}


export async function apiBaseBasePriorityProjectExport(query: TBasePriorityProject): Promise<AxiosResponse<Blob>> {
  return blob({
    url: '/base/basePriorityProject/export',
    method: 'post',
    params: query
  }); 
}


export async function apiBaseBaseEquipmentExport(query: TBaseEquipment): Promise<AxiosResponse<Blob>> {
  return blob({
    url: '/base/baseEquipment/export',
    method: 'post',
    params: query
  }); 
}


export async function apiBaseStopeListAll(query: TBaseStope): Promise<TBaseStope[]> {
  return request<{ data: TBaseStope[] }>({
    url: '/base/stope/listAll',
    method: 'get',
    params: query
  }).then(res => res.data); 
}


export async function apiBaseProjectDepartmentListAll(query: TBaseProjectDepartment): Promise<TBaseProjectDepartment[]> {
  return request<{ data: TBaseProjectDepartment[] }>({
    url: '/base/projectDepartment/listAll',
    method: 'get',
    params: query
  }).then(res => res.data); 
}


export async function apiBasePeriodListAll(query: TBaseWorkingPeriod): Promise<TBaseWorkingPeriod[]> {
  return request<{ data: TBaseWorkingPeriod[] }>({
    url: '/base/period/listAll',
    method: 'get',
    params: query
  }).then(res => res.data); 
}


export async function apiBaseOrePassListAll(query: TBaseOrePass): Promise<TBaseOrePass[]> {
  return request<{ data: TBaseOrePass[] }>({
    url: '/base/orePass/listAll',
    method: 'get',
    params: query
  }).then(res => res.data); 
}


export async function apiBaseFaceListAll(query: TBaseWorkingFace): Promise<TBaseWorkingFace[]> {
  return request<{ data: TBaseWorkingFace[] }>({
    url: '/base/face/listAll',
    method: 'get',
    params: query
  }).then(res => res.data); 
}


export async function apiBaseBasePriorityProjectListAll(query: TBasePriorityProject): Promise<TBasePriorityProjectVo[]> {
  return request<{ data: TBasePriorityProjectVo[] }>({
    url: '/base/basePriorityProject/listAll',
    method: 'get',
    params: query
  }).then(res => res.data); 
}


export async function apiBaseBaseEquipmentListAll(query: TBaseEquipment): Promise<TBaseEquipment[]> {
  return request<{ data: TBaseEquipment[] }>({
    url: '/base/baseEquipment/listAll',
    method: 'get',
    params: query
  }).then(res => res.data); 
}


