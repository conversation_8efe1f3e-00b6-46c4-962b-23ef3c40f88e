<script setup lang="ts">
import { dataBaseUrl } from '@/api/config/base.js'
import { request } from '@/utils/hsj/service/index'
import { Delete, Plus } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { computed, ref, watch } from 'vue'

const pageName = 'dataEquipment'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  equipmentType: {
    type: Number,
    required: true,
  },
  equipmentTypeName: {
    type: String,
    required: true,
  },
  periodOptions: {
    type: Array,
    default: () => [],
  },
  equipmentOptions: {
    type: Array,
    default: () => [],
  },
})

const emit = defineEmits(['update:modelValue', 'refresh'])

const dialogVisible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val),
})

const selectedDate = ref('')

const tableData = ref<any[]>([])
const loading = ref(false)

const formRef = ref<any>(null)
const rules = {
  selectedDate: [{ required: true, message: '请选择日期', trigger: 'change' }],
}

const fetchData = async () => {
  if (!selectedDate.value) {
    ElMessage.warning('请先选择日期')
    return
  }

  loading.value = true
  try {
    const res = await request<any>({
      url: `${dataBaseUrl}/${pageName}/list`,
      method: 'get',
      params: {
        operationDate: selectedDate.value,
        equipmentType: props.equipmentType,
        pageSize: 999,
      },
    })

    if (res.code === 200) {
      tableData.value = res.rows || []
    } else {
      ElMessage.error(res.msg || '获取数据失败')
    }
  } catch (error) {
    console.error('获取数据失败:', error)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

const addRow = () => {
  if (!selectedDate.value) {
    ElMessage.warning('请先选择日期')
    return
  }

  const newRow: any = {
    operationDate: selectedDate.value,
    equipmentType: props.equipmentType,
    equipmentNo: '',
    workingPeriodId: '',
    startTime: '',
    endTime: '',
    totalProcessingVolume: '',
    isNew: true,
  }

  // 电机车特有字段
  if (props.equipmentType === 1) {
    newRow.mineCarsNumber = ''
    newRow.numberOfRunningTrains = ''
  }

  tableData.value.push(newRow)

  setTimeout(() => {
    const selectElements = document.querySelectorAll(
      '.batch-add-dialog .el-table .el-select'
    )
    if (selectElements && selectElements.length > 0) {
      const targetSelect = selectElements[selectElements.length - 1]
      if (targetSelect) {
        const input = targetSelect.querySelector('input')
        if (input) {
          ;(input as HTMLElement).focus()
        } else {
          ;(targetSelect as HTMLElement).click()
        }
      }
    }
  }, 100)
}

const deleteRow = (index: number, _row: any) => {
  tableData.value.splice(index, 1)
}

const handleDialogClosed = () => {
  selectedDate.value = ''
  tableData.value = []
}

// 计算运行时长（分钟）
const calculateOperationTime = (startTime: string, endTime: string): number => {
  if (!startTime || !endTime) return 0

  const start = new Date(`2000-01-01 ${startTime}`)
  const end = new Date(`2000-01-01 ${endTime}`)

  // 如果结束时间小于开始时间，说明跨天了
  if (end < start) {
    end.setDate(end.getDate() + 1)
  }

  const diffMs = end.getTime() - start.getTime()
  return Math.round(diffMs / (1000 * 60)) // 转换为分钟
}

const saveAll = async () => {
  if (!selectedDate.value) {
    ElMessage.warning('请先选择日期')
    return
  }

  if (tableData.value.length === 0) {
    ElMessage.warning('没有数据需要保存')
    return
  }

  // 验证必填字段
  for (const item of tableData.value) {
    if (!item.equipmentNo) {
      ElMessage.warning('请选择设备编号')
      return
    }
    if (!item.startTime) {
      ElMessage.warning('请填写运行开始时间')
      return
    }
    if (!item.endTime) {
      ElMessage.warning('请填写运行结束时间')
      return
    }
  }

  console.log('提交数据:', JSON.stringify(tableData.value, null, 2))

  loading.value = true
  try {
    // 逐个保存数据
    for (const item of tableData.value) {
      const data = {
        ...item,
        operationTime: calculateOperationTime(item.startTime, item.endTime),
      }
      delete data.isNew

      if (item.isNew) {
        // 新增
        await request({
          url: `${dataBaseUrl}/${pageName}`,
          method: 'post',
          data,
        })
      } else {
        // 编辑
        await request({
          url: `${dataBaseUrl}/${pageName}`,
          method: 'put',
          data,
        })
      }
    }

    ElMessage.success('保存成功')
    dialogVisible.value = false
    emit('refresh')
  } catch (error) {
    console.error('保存数据失败:', error)
    ElMessage.error('保存数据失败')
  } finally {
    loading.value = false
  }
}

watch(selectedDate, (newDate) => {
  if (newDate) {
    tableData.value = []
    fetchData()
  }
})

// 获取可用的设备编号选项（避免重复选择）
const getAvailableEquipmentOptions = (currentRow: any) => {
  const selectedEquipmentNos = tableData.value
    .filter((row) => row !== currentRow && row.equipmentNo)
    .map((row) => row.equipmentNo)

  return props.equipmentOptions.filter(
    (item: any) => !selectedEquipmentNos.includes((item as any).value)
  )
}
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    :title="`按日添加${equipmentTypeName}数据`"
    destroy-on-close
    width="1200px"
    :close-on-click-modal="false"
    append-to-body
    class="batch-add-dialog"
    @closed="handleDialogClosed"
  >
    <el-form
      ref="formRef"
      :model="{ selectedDate }"
      :rules="rules"
      label-width="80px"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="选择日期" prop="selectedDate">
            <el-date-picker
              v-model="selectedDate"
              type="date"
              placeholder="请选择日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item>
        <el-button type="success" @click="addRow">
          <el-icon><component :is="Plus" /></el-icon> 添加行
        </el-button>
      </el-form-item>
    </el-form>

    <el-table
      v-loading="loading"
      :data="tableData"
      border
      style="width: 100%"
      max-height="600px"
    >
      <el-table-column label="设备编号" prop="equipmentNo" >
        <template #default="{ row }">
          <el-select
            v-model="row.equipmentNo"
            placeholder="请选择设备编号"
            style="width: 100%"
            filterable
          >
            <el-option
              v-for="item in getAvailableEquipmentOptions(row)"
              :key="(item as any).value"
              :label="(item as any).label"
              :value="(item as any).value"
            />
          </el-select>
        </template>
      </el-table-column>

      <!-- 电机车特有字段 -->
      <el-table-column
        v-if="equipmentType === 1"
        label="编组数量"
        prop="mineCarsNumber"

      >
        <template #default="{ row }">
          <el-input v-model="row.mineCarsNumber" placeholder="编组数量" />
        </template>
      </el-table-column>

      <el-table-column
        v-if="equipmentType === 1"
        label="运行列次"
        prop="numberOfRunningTrains"

      >
        <template #default="{ row }">
          <el-input v-model="row.numberOfRunningTrains" placeholder="运行列次" />
        </template>
      </el-table-column>

      <el-table-column label="运行开始时间" prop="startTime" >
        <template #default="{ row }">
          <el-time-picker
            v-model="row.startTime"
            placeholder="开始时间"
            format="HH:mm"
            value-format="HH:mm:ss"
            style="width: 100%"
          />
        </template>
      </el-table-column>

      <el-table-column label="运行结束时间" prop="endTime" >
        <template #default="{ row }">
          <el-time-picker
            v-model="row.endTime"
            placeholder="结束时间"
            format="HH:mm"
            value-format="HH:mm:ss"
            style="width: 100%"
          />
        </template>
      </el-table-column>

      <el-table-column label="总处理量" prop="totalProcessingVolume" >
        <template #default="{ row }">
          <el-input v-model="row.totalProcessingVolume" placeholder="总处理量" />
        </template>
      </el-table-column>

      <el-table-column label="操作" fixed="right">
        <template #default="{ row, $index }">
          <el-button type="danger" link @click="deleteRow($index, row)">
            <el-icon><component :is="Delete" /></el-icon>
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <template #footer>
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button type="primary" @click="saveAll" :loading="loading"
        >保 存</el-button
      >
    </template>
  </el-dialog>
</template>

<style>
.batch-add-dialog .el-dialog__body {
  padding: 20px !important;
}

.batch-add-dialog .el-table {
  margin-top: 15px;
  width: 100% !important;
}

.batch-add-dialog .el-dialog__header,
.batch-add-dialog .el-dialog__footer {
  padding: 15px 20px !important;
}

.batch-add-dialog .el-dialog__body .el-table__body-wrapper {
  overflow-y: auto;
}

.batch-add-dialog .el-form-item {
  margin-bottom: 18px;
}
</style>
