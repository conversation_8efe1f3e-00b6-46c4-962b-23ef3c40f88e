import request from '@/utils/request'

// 查询设备数据管理列表
export function listDataEquipment(query) {
  return request({
    url: '/data/dataEquipment/list',
    method: 'get',
    params: query
  })
}

// 查询设备数据管理详细
export function getDataEquipment(id) {
  return request({
    url: '/data/dataEquipment/' + id,
    method: 'get'
  })
}

// 新增设备数据管理
export function addDataEquipment(data) {
  return request({
    url: '/data/dataEquipment',
    method: 'post',
    data: data
  })
}

// 修改设备数据管理
export function updateDataEquipment(data) {
  return request({
    url: '/data/dataEquipment',
    method: 'put',
    data: data
  })
}

// 删除设备数据管理
export function delDataEquipment(id) {
  return request({
    url: '/data/dataEquipment/' + id,
    method: 'delete'
  })
}
