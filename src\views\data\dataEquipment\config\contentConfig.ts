export const tableItem: BaseTableItem[] = [
  {
    prop: 'id',
    label: '设备数据ID',
    width: '80',
  },

  {
    prop: 'equipmentNo',
    label: '设备编号',
  },
  {
    prop: 'mineCarsNumber',
    label: '编组数量',
  },
  {
    prop: 'numberOfRunningTrains',
    label: '运行列次',
  },
  {
    prop: 'operationTime',
    label: '运行时长',
  },
  {
    prop: 'totalProcessingVolume',
    label: '总处理量',
  },
  {
    prop: 'operationDate',
    label: '运行日期',
  },
  {
    prop: 'startTime',
    label: '运行开始时间',
  },
  {
    prop: 'endTime',
    label: '运行结束时间',
  },
  {
    prop: 'todo',
    label: '操作',
    width: '200',
    fixed: !window.isSmallScreen ? 'right' : false,
    slotName: 'todo',
    showOverflowTooltip: false,
  },
]
export default (): BaseTableProps => {
  return {
    tableItem,
    elTableConfig: {
      tooltipOptions: {
        popperClass: 'lmw_popper',
        effect: 'light',
      },
      rowKey: 'id',
    },
    showIndex: false,
    showChoose: true,
    pagination: true,
  }
}
