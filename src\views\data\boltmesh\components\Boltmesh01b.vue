<template>
  <div class="w-full">
    <BaseEchart v-if="options" :options="options" height="500px" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import BaseEchart from '@/BaseComponent/BaseEchart/src/echart.vue'
import { ElMessage } from 'element-plus'
import { apiDataStatsBoltmesh01b } from '@/apis/data'
import type { TDataSupportTypeDepartmentWithPlanStats } from '@/apis/model'
import { ToolUtils } from '@/utils/tool'

interface Props {
  dateRange: {
    viewType: string
    startDate: string
    endDate: string
  }
}

const props = defineProps<Props>()

// 图表数据
const chartData = ref<TDataSupportTypeDepartmentWithPlanStats[]>([])
const lastViewType = ref('monthly')

// 获取图表数据
const fetchChartData = async () => {
  try {
    const { viewType, startDate, endDate } = props.dateRange
    if (!startDate || !endDate) {
      return
    }

    const params = {
      viewType,
      startDate,
      endDate,
    }

    const res = await apiDataStatsBoltmesh01b(params)
    chartData.value = res || []
    lastViewType.value = viewType
  } catch (error) {
    console.error('获取锚网支护数据失败:', error)
    ElMessage.error('获取数据失败')
  }
}

// 监听日期范围变化
watch(() => props.dateRange, () => {
  if (props.dateRange?.startDate) {
    fetchChartData()
  }
}, {
  immediate: true,
  deep: true
})

// 格式化日期显示
const formatDisplayDate = (item: any) => ToolUtils.formatDisplayDate(lastViewType.value, item)

// 按日期和项目部分组数据
const groupedData = computed(() => {
  const groups: Record<
    string,
    Record<string, { planSupportLength?: any; totalSupportLength?: any }>
  > = {}

  chartData.value.forEach((item: TDataSupportTypeDepartmentWithPlanStats) => {
    const dateKey = formatDisplayDate(item)

    if (!dateKey) return

    if (!groups[dateKey]) {
      groups[dateKey] = {}
    }

    const department = item.projectDepartmentName || '未知项目部'
    groups[dateKey][department] = {
      planSupportLength: item.planSupportLength,
      totalSupportLength: item.totalSupportLength,
    }
  })

  return groups
})

// 图表配置
const options = computed(() => {
  if (!chartData.value || !chartData.value.length) {
    return {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
      },
      xAxis: {
        type: 'category',
        data: [],
      },
      yAxis: {
        type: 'value',
      },
      series: [],
    }
  }

  // 获取所有唯一的日期
  const dates = Object.keys(groupedData.value).sort()

  // 获取所有项目部
  const departments = Array.from(
    new Set(
      Object.values(groupedData.value).flatMap((departments) =>
        Object.keys(departments)
      )
    )
  ).sort()

  // 获取所有项目部和类型组合
  const departmentAndTypes = departments.reduce((acc, department) => {
    acc.push(department + '计划')
    acc.push(department + '实际')
    return acc
  }, [] as string[])

  let yMax = 0

  const dataMap = departmentAndTypes.reduce(
    (acc, departmentAndType) => {
      const isPlan = departmentAndType.endsWith('计划')
      const department = departmentAndType.replace('计划', '').replace('实际', '')
      acc[departmentAndType] = dates.map((date) => {
        const departmentData = groupedData.value[date][department]
        if (!departmentData) return 0
        const value = isPlan ? departmentData.planSupportLength || 0 : departmentData.totalSupportLength || 0
        yMax = Math.max(yMax, value)
        return value
      })
      return acc
    },
    {} as Record<string, number[]>
  )

  const getMax = (max: number) => {
    max = Math.round(max)
    if (max === 0) return 100
    return (
      (Number(max.toString()[0]) + 1) * Math.pow(10, max.toString().length - 1)
    )
  }
  yMax = getMax(yMax)

  // 生成系列数据
  const series = departmentAndTypes.map((departmentAndType) => {
    const isPlan = departmentAndType.endsWith('计划')
    const itemStyle = {
      color: getColorByDepartment(departmentAndType),
    }

    const data = dataMap[departmentAndType]

    return {
      name: departmentAndType,
      type: 'bar',
      data,
      itemStyle,
      barGap: 0,
      barWidth: '15%',
    }
  })

  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
      formatter: (params: any) => {
        const date = params[0].axisValue
        const departmentData = groupedData.value[date] || {}

        let result = `<div style="margin-bottom: 5px;">${date}</div>`

        // 按项目部分组显示
        departments.forEach(department => {
          const data = departmentData[department]
          if (data && (data.planSupportLength !== undefined || data.totalSupportLength !== undefined)) {
            const planValue = data.planSupportLength || 0;
            const actualValue = data.totalSupportLength || 0;
            const completionRate = planValue > 0 ? ((actualValue / planValue) * 100).toFixed(1) : '0.0';
            
            result += `<div style="margin: 5px 0;">
              <div style="font-weight: bold;">${department}</div>
              ${data.planSupportLength !== undefined ? `<div>计划长度: ${data.planSupportLength}米</div>` : ''}
              ${data.totalSupportLength !== undefined ? `<div>实际长度: ${data.totalSupportLength}米</div>` : ''}
              <div>完成率: ${completionRate}%</div>
            </div>`
          }
        })

        return result
      },
    },
    legend: {
      data: departmentAndTypes,
      bottom: 0,
      left: 'center',
      itemGap: 15,
      itemWidth: 10,
      itemHeight: 10,
      textStyle: {
        fontSize: 11,
        lineHeight: 14,
      },
    },
    grid: {
      left: '3%',
      right: '3%',
      top: '10%',
      bottom: '20%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      boundaryGap: ['5%', '5%'],
      data: dates,
      axisLabel: {
        rotate: 0,
        margin: 15,
        overflow: 'truncate',
        formatter: (value: string) => {
          if (lastViewType.value === 'daily') {
            return value
          } else if (lastViewType.value === 'monthly') {
            return value.replace('年', '年\n')
          }
          return value
        },
      },
      axisTick: {
        alignWithLabel: true,
      },
    },
    yAxis: {
      type: 'value',
      name: '长度(米)',
      min: 0,
      axisLabel: {
        formatter: '{value}',
      },
      axisLine: {
        lineStyle: {
          color: '#5B9BD5',
        },
      },
      nameTextStyle: {
        color: '#5B9BD5',
      },
    },
    series,
  }
})

// 根据项目部获取颜色
const getColorByDepartment = (departmentAndType: string) => {
  // 如果没有预定义颜色，根据项目部名称生成颜色
  const isPlan = departmentAndType.endsWith('计划')
  const colors = ['#5B9BD5', '#70AD47', '#FFC000', '#C55A5A', '#A569BD', '#F39C12', '#48C9B0', '#E74C3C']
  const departmentName = departmentAndType.replace('计划', '').replace('实际', '')
  const hash = departmentName.split('').reduce((a, b) => {
    a = ((a << 5) - a) + b.charCodeAt(0)
    return a & a
  }, 0)
  const colorIndex = Math.abs(hash) % colors.length
  return isPlan ? colors[colorIndex] + '80' : colors[colorIndex]
}
</script>
